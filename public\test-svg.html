<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste SVGs Padrão</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .avatar-test {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .avatar-size {
            text-align: center;
        }
        .avatar-size img {
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .cover-test {
            margin: 20px 0;
        }
        .cover-test img {
            width: 100%;
            max-width: 600px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        h1, h2 {
            color: #333;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Teste das Imagens SVG Padrão</h1>
        
        <div class="info">
            <strong>Objetivo:</strong> Verificar se os novos SVGs estão funcionando corretamente em diferentes tamanhos e contextos.
        </div>

        <!-- Teste do Avatar -->
        <div class="test-section">
            <h2>👤 Avatar Padrão (avatar.svg)</h2>
            <div class="avatar-test">
                <div class="avatar-size">
                    <img src="images/users/avatar.svg" alt="Avatar 40px" width="40" height="40">
                    <p>40x40px</p>
                </div>
                <div class="avatar-size">
                    <img src="images/users/avatar.svg" alt="Avatar 60px" width="60" height="60">
                    <p>60x60px</p>
                </div>
                <div class="avatar-size">
                    <img src="images/users/avatar.svg" alt="Avatar 80px" width="80" height="80">
                    <p>80x80px</p>
                </div>
                <div class="avatar-size">
                    <img src="images/users/avatar.svg" alt="Avatar 120px" width="120" height="120">
                    <p>120x120px</p>
                </div>
                <div class="avatar-size">
                    <img src="images/users/avatar.svg" alt="Avatar 200px" width="200" height="200">
                    <p>200x200px</p>
                </div>
            </div>
            <p class="success">✅ Avatar deve aparecer nítido em todos os tamanhos</p>
        </div>

        <!-- Teste da Capa -->
        <div class="test-section">
            <h2>🎨 Capa Padrão (capa.svg)</h2>
            <div class="cover-test">
                <img src="images/users/capa.svg" alt="Capa padrão">
                <p><strong>Dimensões:</strong> Responsiva (800x320px original)</p>
                <p><strong>Gradiente:</strong> Roxo claro → Roxo escuro</p>
                <p><strong>Elementos:</strong> Formas geométricas decorativas</p>
            </div>
            <p class="success">✅ Capa deve mostrar gradiente roxo suave com elementos decorativos</p>
        </div>

        <!-- Teste de Responsividade -->
        <div class="test-section">
            <h2>📱 Teste de Responsividade</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px;">
                <div style="text-align: center;">
                    <img src="images/users/avatar.svg" alt="Avatar responsivo" style="width: 100%; max-width: 100px; height: auto; border-radius: 50%;">
                    <p>Avatar Responsivo</p>
                </div>
                <div style="text-align: center;">
                    <img src="images/users/capa.svg" alt="Capa responsiva" style="width: 100%; height: 80px; object-fit: cover; border-radius: 8px;">
                    <p>Capa Responsiva</p>
                </div>
            </div>
            <p class="success">✅ Imagens devem se adaptar ao container</p>
        </div>

        <!-- Informações Técnicas -->
        <div class="test-section">
            <h2>🔧 Informações Técnicas</h2>
            <ul>
                <li><strong>Formato:</strong> SVG (Scalable Vector Graphics)</li>
                <li><strong>Vantagens:</strong> Escalável, leve, editável</li>
                <li><strong>Compatibilidade:</strong> Todos os navegadores modernos</li>
                <li><strong>Tamanho:</strong> Muito menor que JPG equivalente</li>
                <li><strong>Qualidade:</strong> Perfeita em qualquer resolução</li>
            </ul>
        </div>

        <!-- Status do Teste -->
        <div class="test-section" style="background: #f8f9fa;">
            <h2>✅ Status do Teste</h2>
            <p>Se você pode ver as imagens acima claramente:</p>
            <ul>
                <li class="success">✅ Avatar SVG está funcionando</li>
                <li class="success">✅ Capa SVG está funcionando</li>
                <li class="success">✅ Responsividade está OK</li>
                <li class="success">✅ Sistema pronto para produção</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p><small>Arquivo de teste criado automaticamente - Pode ser removido após verificação</small></p>
        </div>
    </div>
</body>
</html>
