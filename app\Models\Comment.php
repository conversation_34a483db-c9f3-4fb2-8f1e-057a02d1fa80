<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'post_id',
        'body',
        'edited_at'
    ];

    protected $casts = [
        'edited_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($comment) {
            // Usar o novo sistema de pontos
            \App\Models\UserPoint::addPoints(
                $comment->user_id,
                'comment',
                5,
                "Comentou em uma postagem",
                $comment->id,
                \App\Models\Comment::class
            );
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    /**
     * Verifica se o comentário foi editado
     */
    public function isEdited()
    {
        return !is_null($this->edited_at);
    }

    /**
     * Verifica se o usuário pode editar este comentário
     */
    public function canEdit($user)
    {
        return $user && $this->user_id === $user->id;
    }

    /**
     * Verifica se o usuário pode deletar este comentário
     */
    public function canDelete($user)
    {
        return $user && ($this->user_id === $user->id || $user->isAdmin());
    }

    /**
     * Marca o comentário como editado
     */
    public function markAsEdited()
    {
        $this->edited_at = now();
        $this->save();
    }
}
