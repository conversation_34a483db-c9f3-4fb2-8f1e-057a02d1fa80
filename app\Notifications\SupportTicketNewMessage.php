<?php

namespace App\Notifications;

use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SupportTicketNewMessage extends Notification
{
    use Queueable;

    protected $ticket;
    protected $message;

    public function __construct(SupportTicket $ticket, SupportTicketMessage $message)
    {
        $this->ticket = $ticket;
        $this->message = $message;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Nova Mensagem no Ticket #{$this->ticket->ticket_number}")
            ->view('emails.support.ticket-new-message', [
                'ticket' => $this->ticket,
                'message' => $this->message,
                'user' => $notifiable,
            ]);
    }
}
