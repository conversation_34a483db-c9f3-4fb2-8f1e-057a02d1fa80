<?php

namespace App\Notifications;

use App\Models\SupportTicket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SupportTicketStatusChanged extends Notification
{
    use Queueable;

    protected $ticket;
    protected $oldStatus;
    protected $newStatus;

    public function __construct(SupportTicket $ticket, $oldStatus, $newStatus)
    {
        $this->ticket = $ticket;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $statusLabels = [
            'aberto' => 'Aberto',
            'em_andamento' => 'Em Andamento',
            'aguardando_resposta' => 'Aguardando Resposta',
            'resolvido' => 'Resolvido',
            'fechado' => 'Fechado',
        ];

        $oldStatusLabel = $statusLabels[$this->oldStatus] ?? $this->oldStatus;
        $newStatusLabel = $statusLabels[$this->newStatus] ?? $this->newStatus;

        return (new MailMessage)
            ->subject("Status do Ticket #{$this->ticket->ticket_number} Atualizado")
            ->view('emails.support.ticket-status-changed', [
                'ticket' => $this->ticket,
                'oldStatus' => $oldStatusLabel,
                'newStatus' => $newStatusLabel,
                'user' => $notifiable,
            ]);
    }
}
