<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Status do Ticket Atualizado</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #6366f1;
            margin-bottom: 10px;
        }
        .ticket-info {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-change {
            background-color: #e7f3ff;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            background-color: #6366f1;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{ config('app.name') }}</div>
            <h1>Status do Ticket Atualizado</h1>
        </div>

        <p>Olá {{ $user->name }},</p>

        <p>O status do seu ticket de suporte foi atualizado:</p>

        <div class="ticket-info">
            <h3>Informações do Ticket</h3>
            <p><strong>Número:</strong> #{{ $ticket->ticket_number }}</p>
            <p><strong>Título:</strong> {{ $ticket->title }}</p>
            <p><strong>Categoria:</strong> {{ $ticket->category }}</p>
            <p><strong>Prioridade:</strong> {{ $ticket->priority_label }}</p>
        </div>

        <div class="status-change">
            <h3>Mudança de Status</h3>
            <p><strong>Status Anterior:</strong> {{ $oldStatus }}</p>
            <p><strong>Novo Status:</strong> {{ $newStatus }}</p>
        </div>

        @if($ticket->status === 'resolvido')
            <p>Seu ticket foi marcado como resolvido. Se o problema foi solucionado satisfatoriamente, você pode avaliar nosso atendimento.</p>
        @elseif($ticket->status === 'aguardando_resposta')
            <p>Estamos aguardando sua resposta para continuar com o atendimento.</p>
        @elseif($ticket->status === 'em_andamento')
            <p>Nossa equipe está trabalhando na resolução do seu problema.</p>
        @endif

        <div style="text-align: center;">
            <a href="{{ route('support.tickets.show', $ticket) }}" class="button">
                Ver Ticket
            </a>
        </div>

        <div class="footer">
            <p>Esta é uma mensagem automática. Por favor, não responda este email.</p>
            <p>Para mais informações, acesse sua conta em {{ config('app.name') }}.</p>
        </div>
    </div>
</body>
</html>
