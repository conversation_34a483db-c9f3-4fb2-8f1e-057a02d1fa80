<!-- Tutorials Section -->
<div>
    <!-- Category Filter -->
    @if($this->articleCategories->count() > 0)
        <div class="mb-6">
            <div class="flex flex-wrap gap-2">
                <button
                    wire:click="$set('selectedCategory', '')"
                    class="px-3 py-1 text-sm font-medium rounded-md transition-colors {{ $selectedCategory === '' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600' }}">
                    Todas as Categorias
                </button>
                @foreach($this->articleCategories as $category)
                    <button
                        wire:click="$set('selectedCategory', '{{ $category }}')"
                        class="px-3 py-1 text-sm font-medium rounded-md transition-colors {{ $selectedCategory === $category ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600' }}">
                        {{ ucfirst($category) }}
                    </button>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Featured Articles -->
    @if(!$searchQuery && !$selectedCategory && $this->featuredArticles->count() > 0)
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Tutoriais em Destaque</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($this->featuredArticles as $article)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {{ ucfirst($article->category) }}
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ $article->view_count }} visualizações
                                </span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ $article->title }}</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                {{ Str::limit(strip_tags($article->content), 100) }}
                            </p>
                            <div class="flex items-center justify-between">
                                <a
                                    href="{{ route('support.articles.show', $article) }}"
                                    wire:navigate
                                    class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors">
                                    Ler Tutorial
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                                @if($article->helpful_count > 0 || $article->not_helpful_count > 0)
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                        </svg>
                                        {{ $article->helpful_percentage }}%
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- All Articles -->
    <div>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                @if($searchQuery)
                    Resultados da Busca
                @elseif($selectedCategory)
                    Tutoriais - {{ ucfirst($selectedCategory) }}
                @else
                    Todos os Tutoriais
                @endif
            </h2>
            @if($searchQuery || $selectedCategory)
                <flux:button wire:click="clearSearch" variant="ghost" size="sm">
                    <x-flux::icon icon="x-mark" class="w-4 h-4 mr-1" />
                    Limpar Filtros
                </flux:button>
            @endif
        </div>

        @if($this->helpArticles->count() > 0)
            <div class="space-y-4">
                @foreach($this->helpArticles as $article)
                    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg hover:shadow-md transition-shadow">
                        <div class="p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                            <a href="{{ route('support.articles.show', $article) }}" 
                                               class="hover:text-blue-600 dark:hover:text-blue-400"
                                               wire:navigate>
                                                {{ $article->title }}
                                            </a>
                                        </h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                            {{ ucfirst($article->category) }}
                                        </span>
                                    </div>
                                    <p class="text-gray-600 dark:text-gray-300 mb-3">
                                        {{ Str::limit(strip_tags($article->content), 200) }}
                                    </p>
                                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                        <span class="flex items-center">
                                            <x-flux::icon icon="eye" class="w-4 h-4 mr-1" />
                                            {{ $article->view_count }} visualizações
                                        </span>
                                        @if($article->helpful_count > 0 || $article->not_helpful_count > 0)
                                            <span class="flex items-center">
                                                <x-flux::icon icon="hand-thumb-up" class="w-4 h-4 mr-1" />
                                                {{ $article->helpful_percentage }}% útil
                                            </span>
                                        @endif
                                        <span>{{ $article->created_at->format('d/m/Y') }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <flux:button 
                                        href="{{ route('support.articles.show', $article) }}"
                                        variant="ghost" 
                                        size="sm"
                                        wire:navigate>
                                        <x-flux::icon icon="arrow-right" class="w-4 h-4" />
                                    </flux:button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $this->helpArticles->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <x-flux::icon icon="document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhum tutorial encontrado</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @if($searchQuery)
                        Tente buscar com outros termos ou navegue pelas categorias.
                    @else
                        Não há tutoriais disponíveis no momento.
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>
