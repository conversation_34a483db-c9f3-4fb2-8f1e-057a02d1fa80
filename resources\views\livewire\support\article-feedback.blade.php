<div class="flex items-center gap-4">
    @if($userFeedback)
        <!-- User already gave feedback -->
        <div class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
            <x-flux::icon icon="check-circle" class="w-5 h-5 text-green-500" />
            <span>
                @if($userFeedback === 'helpful')
                    Você marcou este artigo como útil
                @else
                    Você marcou este artigo como não útil
                @endif
            </span>
        </div>
        
        <!-- Show current stats -->
        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span class="flex items-center">
                <x-flux::icon icon="hand-thumb-up" class="w-4 h-4 mr-1 text-green-600" />
                {{ $article->helpful_count }}
            </span>
            <span class="flex items-center">
                <x-flux::icon icon="hand-thumb-down" class="w-4 h-4 mr-1 text-red-600" />
                {{ $article->not_helpful_count }}
            </span>
        </div>
    @else
        <!-- User can give feedback -->
        <flux:button 
            wire:click="markAsHelpful"
            variant="ghost" 
            size="sm"
            class="text-green-600 hover:text-green-700 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20">
            <x-flux::icon icon="hand-thumb-up" class="w-5 h-5 mr-2" />
            Sim, foi útil ({{ $article->helpful_count }})
        </flux:button>
        
        <flux:button 
            wire:click="markAsNotHelpful"
            variant="ghost" 
            size="sm"
            class="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20">
            <x-flux::icon icon="hand-thumb-down" class="w-5 h-5 mr-2" />
            Não foi útil ({{ $article->not_helpful_count }})
        </flux:button>
    @endif
</div>
