@if($showModal)
    <flux:modal wire:model="showModal" class="max-w-2xl">
        <flux:modal.header>
            <flux:heading size="lg">Criar Ticket de Suporte</flux:heading>
        </flux:modal.header>

        <form wire:submit="createTicket">
            <div class="space-y-6">
                <!-- Category -->
                <div>
                    <flux:field>
                        <flux:label>Categoria do Problema</flux:label>
                        <flux:select wire:model="category" placeholder="Selecione uma categoria">
                            @foreach($this->categories as $value => $label)
                                <flux:option value="{{ $value }}">{{ $label }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="category" />
                    </flux:field>
                </div>

                <!-- Priority -->
                <div>
                    <flux:field>
                        <flux:label>Prioridade</flux:label>
                        <flux:select wire:model="priority">
                            @foreach($this->priorities as $value => $label)
                                <flux:option value="{{ $value }}">{{ $label }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="priority" />
                    </flux:field>
                </div>

                <!-- Title -->
                <div>
                    <flux:field>
                        <flux:label>Título do Problema</flux:label>
                        <flux:input 
                            wire:model="title" 
                            placeholder="Descreva brevemente o problema"
                            maxlength="255" />
                        <flux:error name="title" />
                    </flux:field>
                </div>

                <!-- Description -->
                <div>
                    <flux:field>
                        <flux:label>Descrição Detalhada</flux:label>
                        <flux:textarea 
                            wire:model="description" 
                            placeholder="Descreva o problema em detalhes. Inclua passos para reproduzir, mensagens de erro, etc."
                            rows="6" />
                        <flux:error name="description" />
                    </flux:field>
                </div>

                <!-- Attachments -->
                <div>
                    <flux:field>
                        <flux:label>Anexos (Opcional)</flux:label>
                        <div class="space-y-3">
                            <input 
                                type="file" 
                                wire:model="attachments" 
                                multiple
                                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt"
                                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-200 dark:hover:file:bg-blue-800" />
                            
                            @if(!empty($attachments))
                                <div class="space-y-2">
                                    @foreach($attachments as $index => $attachment)
                                        <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                                            <span class="text-sm text-gray-700 dark:text-gray-300">
                                                {{ $attachment->getClientOriginalName() }}
                                                <span class="text-gray-500">({{ number_format($attachment->getSize() / 1024, 1) }} KB)</span>
                                            </span>
                                            <flux:button 
                                                wire:click="removeAttachment({{ $index }})"
                                                variant="ghost" 
                                                size="sm"
                                                class="text-red-600 hover:text-red-700">
                                                <x-flux::icon icon="x-mark" class="w-4 h-4" />
                                            </flux:button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                            
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Tipos permitidos: JPG, PNG, PDF, DOC, DOCX, TXT. Máximo 10MB por arquivo.
                            </p>
                        </div>
                        <flux:error name="attachments.*" />
                    </flux:field>
                </div>

                <!-- Help Text -->
                <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-start gap-3">
                        <x-flux::icon icon="information-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                        <div>
                            <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1">Dicas para um atendimento mais rápido:</h4>
                            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                <li>• Seja específico sobre o problema</li>
                                <li>• Inclua passos para reproduzir o erro</li>
                                <li>• Anexe capturas de tela se necessário</li>
                                <li>• Mencione qual dispositivo/navegador está usando</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <flux:modal.footer>
                <div class="flex gap-2">
                    <flux:button wire:click="closeModal" variant="ghost">
                        Cancelar
                    </flux:button>
                    <flux:button type="submit" variant="primary">
                        <x-flux::icon icon="paper-airplane" class="w-4 h-4 mr-2" />
                        Criar Ticket
                    </flux:button>
                </div>
            </flux:modal.footer>
        </form>
    </flux:modal>
@endif
