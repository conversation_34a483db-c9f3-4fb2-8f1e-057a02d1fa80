<svg width="800" height="320" viewBox="0 0 800 320" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradiente principal roxo -->
    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#6d28d9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5b21b6;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradiente de overlay para profundidade -->
    <radialGradient id="overlayGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#581c87;stop-opacity:0.7" />
    </radialGradient>
    
    <!-- Pa<PERSON><PERSON> de ruído sutil -->
    <filter id="noise">
      <feTurbulence baseFrequency="0.9" numOctaves="1" result="noise" />
      <feColorMatrix in="noise" type="saturate" values="0"/>
      <feComponentTransfer>
        <feFuncA type="discrete" tableValues="0 .5"/>
      </feComponentTransfer>
      <feComposite operator="over" in2="SourceGraphic"/>
    </filter>
  </defs>
  
  <!-- Fundo base com gradiente roxo -->
  <rect width="800" height="320" fill="url(#purpleGradient)" />
  
  <!-- Overlay para profundidade -->
  <rect width="800" height="320" fill="url(#overlayGradient)" />
  
  <!-- Formas geométricas decorativas -->
  <g opacity="0.1">
    <!-- Círculos decorativos -->
    <circle cx="150" cy="80" r="40" fill="#ffffff" />
    <circle cx="650" cy="240" r="60" fill="#ffffff" />
    <circle cx="400" cy="50" r="25" fill="#ffffff" />
    
    <!-- Formas abstratas -->
    <path d="M 0 160 Q 200 120 400 160 T 800 160 L 800 320 L 0 320 Z" fill="#ffffff" opacity="0.05" />
    <path d="M 0 0 Q 300 80 600 40 T 800 60 L 800 0 Z" fill="#ffffff" opacity="0.05" />
  </g>
  
  <!-- Efeito de brilho sutil -->
  <rect width="800" height="320" fill="url(#purpleGradient)" opacity="0.1" filter="url(#noise)" />
</svg>
