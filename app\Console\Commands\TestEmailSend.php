<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Log;

class TestEmailSend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email-send {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email sending functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? '<EMAIL>';
        
        $this->info("🔍 Testando envio de email para: {$email}");
        
        // Check if user exists
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("❌ Usuário com email {$email} não encontrado!");
            
            // Create a test user
            $this->info("📝 Criando usuário de teste...");
            try {
                $user = User::create([
                    'name' => 'Teste Email',
                    'email' => $email,
                    'username' => 'teste_email_' . time(),
                    'password' => bcrypt('password'),
                    'role' => 'vip',
                    'active' => true,
                    'email_verified_at' => now(),
                ]);
                $this->info("✅ Usuário criado: {$user->name} ({$user->email})");
            } catch (\Exception $e) {
                $this->error("❌ Erro ao criar usuário: " . $e->getMessage());
                return 1;
            }
        } else {
            $this->info("✅ Usuário encontrado: {$user->name} ({$user->email})");
        }
        
        // Test 1: Simple email test
        $this->info("\n📧 Teste 1: Email simples");
        try {
            Mail::raw('Este é um teste de email do sistema Desiree.', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Teste de Email - Desiree')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });
            $this->info("✅ Email simples enviado com sucesso!");
        } catch (\Exception $e) {
            $this->error("❌ Erro ao enviar email simples: " . $e->getMessage());
            Log::error('Email test error: ' . $e->getMessage());
        }
        
        // Test 2: Password reset email
        $this->info("\n🔐 Teste 2: Email de reset de senha");
        try {
            $status = Password::sendResetLink(['email' => $email]);
            
            if ($status === Password::RESET_LINK_SENT) {
                $this->info("✅ Email de reset de senha enviado com sucesso!");
            } else {
                $this->error("❌ Falha ao enviar email de reset. Status: {$status}");
            }
        } catch (\Exception $e) {
            $this->error("❌ Erro ao enviar email de reset: " . $e->getMessage());
            Log::error('Password reset email error: ' . $e->getMessage());
        }
        
        // Check logs
        $this->info("\n📋 Verificando logs...");
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            $recentLogs = substr($logs, -2000); // Last 2000 characters
            
            if (strpos($recentLogs, 'ERROR') !== false || strpos($recentLogs, 'Exception') !== false) {
                $this->warn("⚠️  Erros encontrados nos logs. Verifique: {$logFile}");
            } else {
                $this->info("✅ Nenhum erro recente encontrado nos logs");
            }
        }
        
        $this->info("\n🎯 Teste concluído!");
        $this->info("📝 Dicas para troubleshooting:");
        $this->info("   - Verifique se o email chegou na caixa de spam");
        $this->info("   - Confirme as configurações SMTP no .env");
        $this->info("   - Verifique os logs em storage/logs/laravel.log");
        $this->info("   - Teste com um email diferente");
        
        return 0;
    }
}
