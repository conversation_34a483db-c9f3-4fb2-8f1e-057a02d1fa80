<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[ADMIN COPY] Solicitação de Reset de Senha - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc2626, #991b1b);
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            color: #ffffff;
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }
        .admin-badge {
            background-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-top: 10px;
            display: inline-block;
        }
        .content {
            padding: 30px;
        }
        .alert-box {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .alert-box h3 {
            color: #dc2626;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .alert-box p {
            color: #991b1b;
            margin: 0;
            font-size: 14px;
        }
        .user-info {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .user-info h4 {
            margin: 0 0 15px 0;
            color: #1f2937;
            font-size: 16px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: 600;
            color: #4b5563;
        }
        .info-value {
            color: #1f2937;
        }
        .reset-link {
            background-color: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
        }
        .reset-link p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6b7280;
        }
        .reset-link a {
            color: #dc2626;
            word-break: break-all;
            text-decoration: none;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 0;
            color: #6b7280;
            font-size: 12px;
        }
        .timestamp {
            color: #6b7280;
            font-size: 12px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <div class="admin-badge">CÓPIA ADMINISTRATIVA</div>
        </div>
        
        <div class="content">
            <div class="alert-box">
                <h3>🔔 Notificação de Segurança</h3>
                <p>Um usuário solicitou a redefinição de senha. Esta é uma cópia automática para fins de monitoramento de segurança.</p>
            </div>
            
            <div class="user-info">
                <h4>📋 Informações do Usuário</h4>
                <div class="info-row">
                    <span class="info-label">Nome:</span>
                    <span class="info-value">{{ $user->name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ $user->email }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Username:</span>
                    <span class="info-value">{{ $user->username ?? 'N/A' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Role:</span>
                    <span class="info-value">{{ $user->role ?? 'N/A' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Data da Solicitação:</span>
                    <span class="info-value timestamp">{{ now()->format('d/m/Y H:i:s') }}</span>
                </div>
            </div>
            
            <div class="reset-link">
                <p><strong>🔗 Link de Reset Enviado ao Usuário:</strong></p>
                <a href="{{ $resetUrl }}">{{ $resetUrl }}</a>
            </div>
            
            <div style="background-color: #eff6ff; border: 1px solid #3b82f6; border-radius: 6px; padding: 16px; margin: 20px 0;">
                <p style="margin: 0; color: #1e40af; font-size: 14px;">
                    <strong>ℹ️ Informação:</strong> Este link expirará em 60 minutos. Se houver suspeita de atividade maliciosa, entre em contato com o usuário ou tome as medidas de segurança apropriadas.
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>© {{ date('Y') }} {{ config('app.name') }} - Sistema de Notificação Administrativa</p>
            <p>Este email foi enviado automaticamente para fins de auditoria e segurança.</p>
        </div>
    </div>
</body>
</html>
