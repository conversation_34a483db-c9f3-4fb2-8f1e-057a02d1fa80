<?php

use App\Models\User;
use App\Models\Post;
use App\Models\UserPhoto;
use App\Models\UserCoverPhoto;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Volt\Component;

new class extends Component {
    public User $user;

    public function mount(): void
    {
        $this->user = Auth::user();
    }

    public function postsCount(): int
    {
        return Post::where('user_id', $this->user->id)->count();
    }

    public function followingCount(): int
    {
        return $this->user->following()->count();
    }

    public function followersCount(): int
    {
        return $this->user->followers()->count();
    }

    public function avatar(): ?string
    {
        $path = UserPhoto::where('user_id', $this->user->id)->latest()->value('photo_path');
        return $path ? Storage::url($path) : null;
    }

    public function cover(): ?string
    {
        $coverPhoto = UserCoverPhoto::where('user_id', $this->user->id)->latest()->first();
        if (!$coverPhoto) {
            return null;
        }

        // Usar a versão recortada se disponível, caso contrário usar a original
        $path = $coverPhoto->cropped_photo_path ?? $coverPhoto->photo_path;
        return $path ? Storage::url($path) : null;
    }

    public function showUserPosts(): void
    {
        $this->dispatch('show-user-posts', userId: $this->user->id);
    }

    public function showUserFollowing(): void
    {
        $this->dispatch('show-user-following', userId: $this->user->id);
    }

    public function showUserFollowers(): void
    {
        $this->dispatch('show-user-followers', userId: $this->user->id);
    }

    /**
     * Verifica se o usuário logado é o dono do perfil
     */
    public function isOwner(): bool
    {
        return Auth::check() && Auth::id() === $this->user->id;
    }

    /**
     * Redireciona para a página de edição de avatar
     */
    public function editAvatar()
    {
        if (!$this->isOwner()) {
            return;
        }

        return redirect()->route('settings.profile-with-avatar');
    }

    /**
     * Redireciona para a página de edição de capa
     */
    public function editCover()
    {
        if (!$this->isOwner()) {
            return;
        }

        return redirect()->route('settings.profile-with-cover');
    }

    /**
     * Redireciona para a página de edição de perfil
     */
    public function editProfile()
    {
        if (!$this->isOwner()) {
            return;
        }

        return redirect()->route('settings.profile');
    }
}; ?>

<div>
    {{-- Componentes modais necessários --}}
    <livewire:user-posts />
    <livewire:user-following />
    <livewire:user-followers />

    <div id="Container" class="pb-6 border border-neutral-200 dark:border-neutral-700 relative rounded-lg shadow-md">
        <div id="capa" class="relative h-32 bg-cover bg-center rounded-t-lg group"
            style="background:url({{ $this->cover() ?? asset('images/users/capa.svg') }}); background-size: cover; background-position: center;">

            @if($this->isOwner())
                <!-- Botão para editar capa -->
                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <x-flux:button
                        wire:click="editCover"
                        variant="primary"
                        size="sm"
                        icon="photo"
                        class="text-white bg-zinc-800 hover:bg-zinc-700 border-none shadow-lg"
                    >
                        Editar Capa
                    </x-flux:button>
                </div>
            @endif
        </div>
        <div id="container_user"  class="relative z-10 -mt-12 flex flex-col items-center">
            <div id="avatar" class="relative group">
                <img src="{{ $this->avatar() ?? asset('images/users/avatar.svg') }}"
                alt="Foto de Perfil" class="w-24 h-24 rounded-full border-4 border-white shadow-lg">
                <livewire:user-status-indicator :userId="$user->id" />

                @if($this->isOwner())
                    <!-- Overlay para editar avatar -->
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center rounded-full">
                        <x-flux:button
                            wire:click="editAvatar"
                            variant="primary"
                            size="sm"
                            icon="camera"
                            class="text-white bg-zinc-800 hover:bg-zinc-700 border-none"
                        >
                            Editar
                        </x-flux:button>
                    </div>
                @endif
            </div>
            <h2 class="text-title text-xl font-semibold mt-2">{{ $user->name }}</h2>
            <p class="text-body-light">
                <a href="{{ route('user.profile', ['username' => $user->username]) }}" class="text-link hover:underline">
                    {{ '@' . $user->username }}
                </a>
            </p>
            <div id="info_user" class="mt-4 flex justify-around w-full">
                <div class="text-center cursor-pointer" wire:click="showUserPosts">
                    <p class="text-subtitle text-lg font-semibold">{{ $this->postsCount() }}</p>
                    <p class="text-body-lighter hover:text-link hover:underline">Posts</p>
                </div>
                <div class="text-center cursor-pointer" wire:click="showUserFollowing">
                    <p class="text-subtitle text-lg font-semibold">{{ $this->followingCount() }}</p>
                    <p class="text-body-lighter hover:text-link hover:underline">Seguindo</p>
                </div>
                <div class="text-center cursor-pointer" wire:click="showUserFollowers">
                    <p class="text-subtitle text-lg font-semibold">{{ $this->followersCount() }}</p>
                    <p class="text-body-lighter hover:text-link hover:underline">Seguidores</p>
                </div>
            </div>

            @if($this->isOwner())
                <!-- Botão para editar perfil -->
                <div class="mt-4">
                    <x-flux:button
                        wire:click="editProfile"
                        variant="primary"
                        size="sm"
                        icon="pencil"
                        class="text-white bg-zinc-700 hover:bg-zinc-600 border-none"
                    >
                        Editar Perfil
                    </x-flux:button>
                </div>
            @endif
        </div>
    </div>
</div>
