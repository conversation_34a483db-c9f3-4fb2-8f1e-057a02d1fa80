<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\ResetPassword as BaseResetPassword;
use Illuminate\Notifications\Messages\MailMessage;
use App\Models\User;
use Illuminate\Support\Facades\Notification;

class CustomResetPassword extends BaseResetPassword
{
    /**
     * Build the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        $resetUrl = url(route('password.reset', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));

        $mailMessage = (new MailMessage)
            ->subject('Redefinição de Senha - ' . config('app.name'))
            ->view('emails.reset-password', [
                'user' => $notifiable,
                'resetUrl' => $resetUrl,
                'token' => $this->token,
            ]);

        // Send copy to admins and contact email
        $this->sendCopyToAdmins($notifiable, $resetUrl);

        return $mailMessage;
    }

    /**
     * Send copy of password reset email to admins and contact email
     */
    protected function sendCopyToAdmins($user, $resetUrl)
    {
        // Get admin users
        $adminUsers = User::where('role', 'admin')->get();

        // Contact email
        $contactEmail = '<EMAIL>';

        // Prepare admin notification data
        $adminData = [
            'user' => $user,
            'resetUrl' => $resetUrl,
            'isAdminCopy' => true,
        ];

        // Send to admin users
        foreach ($adminUsers as $admin) {
            try {
                $admin->notify(new AdminPasswordResetCopy($adminData));
            } catch (\Exception $e) {
                \Log::error('Failed to send admin copy to user ' . $admin->id . ': ' . $e->getMessage());
            }
        }

        // Send to contact email
        try {
            Notification::route('mail', $contactEmail)
                ->notify(new AdminPasswordResetCopy($adminData));
        } catch (\Exception $e) {
            \Log::error('Failed to send admin copy to contact email: ' . $e->getMessage());
        }
    }
}
