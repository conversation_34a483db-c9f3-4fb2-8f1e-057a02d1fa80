<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Gerenciar Tickets de Suporte</h1>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 text-center p-4">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $this->stats['total'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Total</div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 text-center p-4">
                <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $this->stats['open'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Abertos</div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 text-center p-4">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $this->stats['closed'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Fechados</div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 text-center p-4">
                <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $this->stats['high_priority'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Alta Prioridade</div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 text-center p-4">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $this->stats['unassigned'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Não Atribuídos</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Buscar</label>
                    <div class="relative">
                        <input
                            wire:model.live.debounce.300ms="searchQuery"
                            placeholder="Número, título, usuário..."
                            class="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                    <select wire:model.live="statusFilter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Todos</option>
                        <option value="aberto">Aberto</option>
                        <option value="em_andamento">Em Andamento</option>
                        <option value="aguardando_resposta">Aguardando Resposta</option>
                        <option value="resolvido">Resolvido</option>
                        <option value="fechado">Fechado</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Prioridade</label>
                    <select wire:model.live="priorityFilter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Todas</option>
                        <option value="baixa">Baixa</option>
                        <option value="media">Média</option>
                        <option value="alta">Alta</option>
                        <option value="urgente">Urgente</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Categoria</label>
                    <select wire:model.live="categoryFilter" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">Todas</option>
                        <option value="conta">Conta</option>
                        <option value="pagamento">Pagamento</option>
                        <option value="tecnico">Técnico</option>
                        <option value="funcionalidade">Funcionalidade</option>
                        <option value="loja">Loja</option>
                        <option value="grupos">Grupos</option>
                        <option value="mensagens">Mensagens</option>
                        <option value="perfil">Perfil</option>
                        <option value="outros">Outros</option>
                    </select>
                </div>
            </div>

            @if($searchQuery || $statusFilter || $priorityFilter || $categoryFilter)
                <div class="flex justify-end">
                    <button wire:click="clearFilters" class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Limpar Filtros
                    </button>
                </div>
            @endif
        </div>
    </div>

    <!-- Tickets Table -->
    @if($this->tickets->count() > 0)
        <div class="space-y-4">
            @foreach($this->tickets as $ticket)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        <a href="{{ route('support.tickets.show', $ticket) }}" 
                                           class="hover:text-blue-600 dark:hover:text-blue-400"
                                           wire:navigate>
                                            #{{ $ticket->ticket_number }} - {{ $ticket->title }}
                                        </a>
                                    </h3>
                                    
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($ticket->status_color === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                        @elseif($ticket->status_color === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                        @elseif($ticket->status_color === 'orange') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                        @elseif($ticket->status_color === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                        {{ $ticket->status_label }}
                                    </span>
                                    
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($ticket->priority_color === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @elseif($ticket->priority_color === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                        @elseif($ticket->priority_color === 'orange') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                        @elseif($ticket->priority_color === 'red') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                        {{ $ticket->priority_label }}
                                    </span>
                                </div>
                                
                                <p class="text-gray-600 dark:text-gray-300 mb-3">
                                    {{ Str::limit($ticket->description, 200) }}
                                </p>
                                
                                <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                    <span class="flex items-center">
                                        <x-flux::icon icon="user" class="w-4 h-4 mr-1" />
                                        {{ $ticket->user->name }}
                                    </span>
                                    <span class="flex items-center">
                                        <x-flux::icon icon="folder" class="w-4 h-4 mr-1" />
                                        {{ $ticket->category }}
                                    </span>
                                    <span class="flex items-center">
                                        <x-flux::icon icon="calendar" class="w-4 h-4 mr-1" />
                                        {{ $ticket->created_at->format('d/m/Y H:i') }}
                                    </span>
                                    @if($ticket->assignedTo)
                                        <span class="flex items-center">
                                            <x-flux::icon icon="user-circle" class="w-4 h-4 mr-1" />
                                            Atribuído a {{ $ticket->assignedTo->name }}
                                        </span>
                                    @else
                                        <span class="flex items-center text-orange-600 dark:text-orange-400">
                                            <x-flux::icon icon="exclamation-triangle" class="w-4 h-4 mr-1" />
                                            Não atribuído
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="ml-4 flex flex-col gap-2">
                                <!-- Status Actions -->
                                <div class="flex gap-1">
                                    @if($ticket->status !== 'em_andamento')
                                        <flux:button 
                                            wire:click="changeStatus({{ $ticket->id }}, 'em_andamento')"
                                            variant="ghost" 
                                            size="sm"
                                            class="text-yellow-600 hover:text-yellow-700">
                                            <x-flux::icon icon="play" class="w-4 h-4" />
                                        </flux:button>
                                    @endif
                                    
                                    @if($ticket->status !== 'resolvido' && $ticket->isOpen())
                                        <flux:button 
                                            wire:click="changeStatus({{ $ticket->id }}, 'resolvido')"
                                            variant="ghost" 
                                            size="sm"
                                            class="text-green-600 hover:text-green-700">
                                            <x-flux::icon icon="check" class="w-4 h-4" />
                                        </flux:button>
                                    @endif
                                    
                                    @if($ticket->status !== 'fechado')
                                        <flux:button 
                                            wire:click="changeStatus({{ $ticket->id }}, 'fechado')"
                                            variant="ghost" 
                                            size="sm"
                                            class="text-gray-600 hover:text-gray-700">
                                            <x-flux::icon icon="x-mark" class="w-4 h-4" />
                                        </flux:button>
                                    @endif
                                </div>
                                
                                <!-- Other Actions -->
                                <div class="flex gap-1">
                                    <flux:button 
                                        wire:click="openAssignModal({{ $ticket->id }})"
                                        variant="ghost" 
                                        size="sm"
                                        class="text-blue-600 hover:text-blue-700">
                                        <x-flux::icon icon="user-plus" class="w-4 h-4" />
                                    </flux:button>
                                    
                                    <flux:button 
                                        href="{{ route('support.tickets.show', $ticket) }}"
                                        variant="ghost" 
                                        size="sm"
                                        wire:navigate>
                                        <x-flux::icon icon="eye" class="w-4 h-4" />
                                    </flux:button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $this->tickets->links() }}
        </div>
    @else
        <div class="text-center py-12">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhum ticket encontrado</h3>
            <p class="text-gray-600 dark:text-gray-300">
                @if($searchQuery || $statusFilter || $priorityFilter || $categoryFilter)
                    Tente ajustar os filtros para encontrar tickets.
                @else
                    Não há tickets de suporte no momento.
                @endif
            </p>
        </div>
    @endif

    <!-- Assign Modal -->
    @if($showAssignModal && $selectedTicket)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" wire:click="closeAssignModal"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                            Atribuir Ticket #{{ $selectedTicket->ticket_number }}
                        </h3>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Atribuir para</label>
                                <select wire:model="assignedTo" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">Não atribuído</option>
                                    @foreach($this->admins as $admin)
                                        <option value="{{ $admin->id }}">{{ $admin->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="assignTicket" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Atribuir
                        </button>
                        <button wire:click="closeAssignModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-700">
                            Cancelar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
