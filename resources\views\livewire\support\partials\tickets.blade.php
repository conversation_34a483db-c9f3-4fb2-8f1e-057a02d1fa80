<!-- Tickets Section -->
<div>
    <!-- Header with Create Button -->
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Meus Tickets de Suporte</h2>
        <flux:button wire:click="openCreateTicket" variant="primary">
            <x-flux::icon icon="plus" class="w-4 h-4 mr-2" />
            Novo Ticket
        </flux:button>
    </div>

    @if($this->userTickets->count() > 0)
        <div class="space-y-4">
            @foreach($this->userTickets as $ticket)
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        <a href="{{ route('support.tickets.show', $ticket) }}" 
                                           class="hover:text-blue-600 dark:hover:text-blue-400"
                                           wire:navigate>
                                            #{{ $ticket->ticket_number }} - {{ $ticket->title }}
                                        </a>
                                    </h3>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($ticket->status_color === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                        @elseif($ticket->status_color === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                        @elseif($ticket->status_color === 'orange') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                        @elseif($ticket->status_color === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                        {{ $ticket->status_label }}
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($ticket->priority_color === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @elseif($ticket->priority_color === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                        @elseif($ticket->priority_color === 'orange') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                                        @elseif($ticket->priority_color === 'red') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                        {{ $ticket->priority_label }}
                                    </span>
                                </div>
                                
                                <p class="text-gray-600 dark:text-gray-300 mb-3">
                                    {{ Str::limit($ticket->description, 200) }}
                                </p>
                                
                                <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                    <span class="flex items-center">
                                        <x-flux::icon icon="folder" class="w-4 h-4 mr-1" />
                                        {{ $ticket->category }}
                                    </span>
                                    <span class="flex items-center">
                                        <x-flux::icon icon="chat-bubble-left-right" class="w-4 h-4 mr-1" />
                                        {{ $ticket->messages->count() }} mensagens
                                    </span>
                                    <span class="flex items-center">
                                        <x-flux::icon icon="calendar" class="w-4 h-4 mr-1" />
                                        {{ $ticket->created_at->format('d/m/Y H:i') }}
                                    </span>
                                    @if($ticket->assignedTo)
                                        <span class="flex items-center">
                                            <x-flux::icon icon="user" class="w-4 h-4 mr-1" />
                                            Atribuído a {{ $ticket->assignedTo->name }}
                                        </span>
                                    @endif
                                </div>

                                <!-- Rating Display -->
                                @if($ticket->rating)
                                    <div class="mt-3 flex items-center gap-2">
                                        <span class="text-sm text-gray-600 dark:text-gray-300">Sua avaliação:</span>
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <x-flux::icon 
                                                    icon="star" 
                                                    class="w-4 h-4 {{ $i <= $ticket->rating ? 'text-yellow-400' : 'text-gray-300' }}" 
                                                    :solid="$i <= $ticket->rating" />
                                            @endfor
                                        </div>
                                        @if($ticket->rating_comment)
                                            <span class="text-sm text-gray-600 dark:text-gray-300">- {{ $ticket->rating_comment }}</span>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            
                            <div class="ml-4 flex flex-col gap-2">
                                <flux:button 
                                    href="{{ route('support.tickets.show', $ticket) }}"
                                    variant="ghost" 
                                    size="sm"
                                    wire:navigate>
                                    <x-flux::icon icon="eye" class="w-4 h-4 mr-1" />
                                    Ver Detalhes
                                </flux:button>
                                
                                @if($ticket->canBeRated())
                                    <flux:button 
                                        href="{{ route('support.tickets.show', $ticket) }}"
                                        variant="primary" 
                                        size="sm"
                                        wire:navigate>
                                        <x-flux::icon icon="star" class="w-4 h-4 mr-1" />
                                        Avaliar
                                    </flux:button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $this->userTickets->links() }}
        </div>
    @else
        <div class="text-center py-12">
            <x-flux::icon icon="ticket" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhum ticket encontrado</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
                Você ainda não criou nenhum ticket de suporte. Quando precisar de ajuda, estaremos aqui!
            </p>
            <flux:button wire:click="openCreateTicket" variant="primary">
                <x-flux::icon icon="plus" class="w-4 h-4 mr-2" />
                Criar Primeiro Ticket
            </flux:button>
        </div>
    @endif

    <!-- Quick Help -->
    <div class="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div class="flex items-start gap-4">
            <div class="flex-shrink-0">
                <x-flux::icon icon="information-circle" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">Precisa de Ajuda Rápida?</h3>
                <p class="text-blue-800 dark:text-blue-200 mb-4">
                    Antes de criar um ticket, que tal verificar nossos tutoriais e FAQ? Muitas dúvidas podem ser resolvidas rapidamente!
                </p>
                <div class="flex gap-3">
                    <flux:button 
                        wire:click="setActiveTab('tutorials')"
                        variant="ghost" 
                        size="sm"
                        class="text-blue-700 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-300 dark:hover:text-blue-200 dark:hover:bg-blue-800/30">
                        <x-flux::icon icon="book-open" class="w-4 h-4 mr-1" />
                        Ver Tutoriais
                    </flux:button>
                    <flux:button 
                        wire:click="setActiveTab('faq')"
                        variant="ghost" 
                        size="sm"
                        class="text-blue-700 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-300 dark:hover:text-blue-200 dark:hover:bg-blue-800/30">
                        <x-flux::icon icon="question-mark-circle" class="w-4 h-4 mr-1" />
                        Ver FAQ
                    </flux:button>
                </div>
            </div>
        </div>
    </div>
</div>
