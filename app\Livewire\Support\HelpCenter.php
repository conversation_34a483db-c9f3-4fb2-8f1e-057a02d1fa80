<?php

namespace App\Livewire\Support;

use App\Models\HelpArticle;
use App\Models\FaqItem;
use App\Models\SupportTicket;
use Livewire\Component;
use Livewire\WithPagination;

class HelpCenter extends Component
{
    use WithPagination;

    public $activeTab = 'tutorials';
    public $searchQuery = '';
    public $selectedCategory = '';
    public $showCreateTicket = false;

    protected $queryString = [
        'activeTab' => ['except' => 'tutorials'],
        'searchQuery' => ['except' => ''],
        'selectedCategory' => ['except' => ''],
    ];

    public function mount()
    {
        // Se o usuário tem tickets abertos, mostrar aba de tickets por padrão
        if (auth()->user()->supportTickets()->open()->exists()) {
            $this->activeTab = 'tickets';
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function updatedSearchQuery()
    {
        $this->resetPage();
    }

    public function updatedSelectedCategory()
    {
        $this->resetPage();
    }

    public function clearSearch()
    {
        $this->searchQuery = '';
        $this->selectedCategory = '';
        $this->resetPage();
    }

    protected $listeners = [
        'ticketCreated' => '$refresh',
        'ticketUpdated' => '$refresh',
    ];

    public function getUserTicketsProperty()
    {
        if (!auth()->check()) {
            return collect();
        }

        return auth()->user()->supportTickets()
            ->with(['assignedTo', 'messages'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getHelpArticlesProperty()
    {
        return collect(); // Retorna coleção vazia por enquanto
    }

    public function getFaqItemsProperty()
    {
        return collect(); // Retorna coleção vazia por enquanto
    }

    public function getFeaturedArticlesProperty()
    {
        return collect(); // Retorna coleção vazia por enquanto
    }

    public function getFeaturedFaqsProperty()
    {
        return collect(); // Retorna coleção vazia por enquanto
    }

    public function getArticleCategoriesProperty()
    {
        return collect(); // Retorna coleção vazia por enquanto
    }

    public function getFaqCategoriesProperty()
    {
        return collect(); // Retorna coleção vazia por enquanto
    }

    public function markArticleAsHelpful($articleId)
    {
        $this->dispatch('notify', [
            'message' => 'Funcionalidade em desenvolvimento!',
            'type' => 'info'
        ]);
    }

    public function markArticleAsNotHelpful($articleId)
    {
        $this->dispatch('notify', [
            'message' => 'Funcionalidade em desenvolvimento!',
            'type' => 'info'
        ]);
    }

    public function markFaqAsHelpful($faqId)
    {
        $this->dispatch('notify', [
            'message' => 'Funcionalidade em desenvolvimento!',
            'type' => 'info'
        ]);
    }

    public function markFaqAsNotHelpful($faqId)
    {
        $this->dispatch('notify', [
            'message' => 'Funcionalidade em desenvolvimento!',
            'type' => 'info'
        ]);
    }

    public function render()
    {
        return view('livewire.support.help-center-simple')
            ->layout('layouts.app', ['title' => 'Central de Ajuda']);
    }
}
