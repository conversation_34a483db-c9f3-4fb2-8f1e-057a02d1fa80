<?php

use Illuminate\Support\Facades\Password;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component
 {
    public string $email = '';
    public bool $isLoading = false;

    /**
     * Send a password reset link to the provided email address.
     */
    public function sendPasswordResetLink(): void
    {
        $this->isLoading = true;

        $this->validate([
            'email' => ['required', 'string', 'email', 'max:255'],
        ], [
            'email.required' => 'O campo email é obrigatório.',
            'email.email' => 'Por favor, insira um endereço de email válido.',
            'email.max' => 'O email não pode ter mais de 255 caracteres.',
        ]);

        try {
            $status = Password::sendResetLink($this->only('email'));

            if ($status === Password::RESET_LINK_SENT) {
                session()->flash('status', 'Um link de redefinição de senha foi enviado para seu email, caso a conta exista.');
                session()->flash('status_type', 'success');
            } else {
                session()->flash('status', 'Um link de redefinição de senha foi enviado para seu email, caso a conta exista.');
                session()->flash('status_type', 'info');
            }
        } catch (\Exception $e) {
            session()->flash('status', 'Ocorreu um erro ao processar sua solicitação. Tente novamente.');
            session()->flash('status_type', 'error');
        }

        $this->isLoading = false;
    }
}; ?>

<div class="flex flex-col gap-6">
    <x-auth-header
        title="Esqueceu a Senha?"
        description="Digite seu email para receber um link de redefinição de senha"
    />

    <!-- Session Status -->
    @if (session('status'))
        <div class="text-center p-4 rounded-lg {{ session('status_type') === 'success' ? 'bg-green-50 text-green-700 border border-green-200' : (session('status_type') === 'error' ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-blue-50 text-blue-700 border border-blue-200') }}">
            <div class="flex items-center justify-center gap-2">
                @if (session('status_type') === 'success')
                    <flux:icon.check-circle class="w-5 h-5" />
                @elseif (session('status_type') === 'error')
                    <flux:icon.exclamation-circle class="w-5 h-5" />
                @else
                    <flux:icon.information-circle class="w-5 h-5" />
                @endif
                <span class="font-medium">{{ session('status') }}</span>
            </div>
        </div>
    @endif

    <form wire:submit="sendPasswordResetLink" class="flex flex-col gap-6">
        <!-- Email Address -->
        <flux:input
            wire:model="email"
            label="Endereço de Email"
            type="email"
            required
            autofocus
            placeholder="<EMAIL>"
            :disabled="$isLoading"
        />

        <flux:button
            variant="primary"
            type="submit"
            class="w-full"
            :disabled="$isLoading"
        >
            @if ($isLoading)
                <flux:icon.arrow-path class="w-4 h-4 animate-spin mr-2" />
                Enviando...
            @else
                <flux:icon.envelope class="w-4 h-4 mr-2" />
                Enviar Link de Redefinição
            @endif
        </flux:button>
    </form>

    <div class="space-x-1 text-center text-sm text-zinc-400">
        Lembrou da senha?
        <flux:link :href="route('login')" wire:navigate class="text-zinc-600 hover:text-zinc-800 font-medium">
            Fazer login
        </flux:link>
    </div>

    <!-- Security Notice -->
    <div class="bg-zinc-50 border border-zinc-200 rounded-lg p-4 text-sm text-zinc-600">
        <div class="flex items-start gap-2">
            <flux:icon.shield-check class="w-5 h-5 text-zinc-400 mt-0.5 flex-shrink-0" />
            <div>
                <p class="font-medium text-zinc-700 mb-1">Segurança</p>
                <p>Por motivos de segurança, enviaremos o link apenas se o email estiver cadastrado em nosso sistema. O link expirará em 60 minutos.</p>
            </div>
        </div>
    </div>
</div>
